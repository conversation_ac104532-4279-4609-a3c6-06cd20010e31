import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:intl/intl.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/ball_rating/presentation/bloc/ball_rating_bloc.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_earning_match_entity.dart';
import 'package:echipta/features/common/widgets/w_empty.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:gap/gap.dart';

class BallEarningView extends StatelessWidget {
  const BallEarningView({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main earning methods
          Text(
            'Asosiy ball yig\'ish usullari:',
            style: context.textTheme.titleMedium!.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.black,
            ),
          ),
          const Gap(16),
          _buildEarningMethod(context, '✅', 'Ro\'yxatdan o\'tish', '3 ball'),
          _buildEarningMethod(context, '🎟', 'Chipta sotib olish', '3 ball'),
          _buildEarningMethod(context, '🔍', 'Chipta skaner qilish (stadionga kirish)', '2 ball'),
          _buildEarningMethod(context, '🎁', 'Do\'stga chipta sovg\'a qilish', '2 ball'),
          _buildEarningMethod(context, '🆔', 'ID karta sotib olish', '10 ball'),
          _buildEarningMethod(context, '🎫', 'Mavsumiy karta sotib olish', '20 ball'),
          _buildEarningMethod(context, '🏟', 'Stadion ichida har 15 000 UZS xarid', '1 ball'),
          _buildEarningMethod(context, '🤝', 'Ilovaga 5 do\'stini taklif qilish', '3 ball'),

          const Gap(32),

          // Additional earning methods
          Text(
            'Qo\'shimcha ball yig\'ish usullari:',
            style: context.textTheme.titleMedium!.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.black,
            ),
          ),
          const Gap(16),
          _buildEarningMethod(context, '⚽️', '1 mavsumda 15+ o\'yin', '30 ball'),
          _buildEarningMethod(context, '🚀', 'Ball Boost kunlarida', '2x ball'),
          _buildEarningMethod(context, '🌟', 'Oltin chipta aksiyasi', '20 ball'),
        ],
      ),
    );
  }

  Widget _buildEarningMethod(BuildContext context, String emoji, String title, String points) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Text(
            emoji,
            style: const TextStyle(fontSize: 16),
          ),
          const Gap(12),
          Expanded(
            child: Text(
              title,
              style: context.textTheme.bodyMedium!.copyWith(
                color: AppColors.black,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            points,
            style: context.textTheme.bodyMedium!.copyWith(
              color: AppColors.green,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

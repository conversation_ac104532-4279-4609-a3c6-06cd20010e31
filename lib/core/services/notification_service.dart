import 'dart:async';
import 'dart:io';
import 'dart:typed_data';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart'
    hide GroupAlertBehavior;
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:go_router/go_router.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import 'package:http/http.dart' as http;
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';

import 'package:echipta/core/api/dio_settings.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';

/// Unified Notification Service
///
/// Handles Firebase Cloud Messaging and Flutter Local Notifications
/// - FCM for push notifications from server
/// - Flutter Local Notifications for local notifications
/// - Background and foreground message handling
/// - Internet connectivity monitoring
/// - Go Router navigation integration
/// - Fixed iOS image support with local file caching
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();

  factory NotificationService() => _instance;

  NotificationService._internal();

  // Firebase components
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications =
  FlutterLocalNotificationsPlugin();

  // Internet connectivity
  StreamSubscription<InternetConnectionStatus>? _connectivitySubscription;

  // Navigation context
  static BuildContext? _navigationContext;

  // Notification channel for Android
  static const AndroidNotificationChannel _channel = AndroidNotificationChannel(
    'echipta_notifications',
    'Echipta Notifications',
    description: 'Echipta ilovasi uchun bildirishnomalar',
    importance: Importance.max,
    playSound: true,
    enableVibration: true,
  );

  String? _fcmToken;
  static bool _initialized = false;
  static bool _firebaseInitialized = false;

  /// Get FCM token
  String? get fcmToken => _fcmToken;

  /// Set navigation context for routing
  static void setNavigationContext(BuildContext context) {
    _navigationContext = context;
  }

  /// Initialize notification service
  static Future<void> initialize() async {
    if (_initialized) return;

    try {
      await Firebase.initializeApp();
      await NotificationService()._initializeLocal();
      await NotificationService()._initializeFirebase();
      await NotificationService()._requestInitialPermissions();
      await NotificationService()._setupConnectivityMonitoring();

      _initialized = true;
      print("✅ Notification Service initialized successfully");
    } catch (e) {
      print("❌ Notification Service initialization failed: $e");
    }
  }

  /// Request initial permissions on startup
  Future<void> _requestInitialPermissions() async {
    print("📱 Requesting initial notification permissions...");

    try {
      // Request Android notification permission (Android 13+)
      final Permission notificationPermission = Permission.notification;
      final PermissionStatus status = await notificationPermission.request();

      print("📱 Initial permission request result: $status");

      if (status == PermissionStatus.granted) {
        print("✅ Notification permissions granted on startup");
      } else {
        print("❌ Notification permissions denied on startup");
      }
    } catch (e) {
      print("❌ Error requesting initial permissions: $e");
    }
  }

  /// Setup internet connectivity monitoring
  Future<void> _setupConnectivityMonitoring() async {
    _connectivitySubscription = InternetConnectionChecker().onStatusChange
        .listen((InternetConnectionStatus status) async {
      switch (status) {
        case InternetConnectionStatus.connected:
          print('📶 Internet connected - checking Firebase initialization');
          if (!_firebaseInitialized) {
            await _initializeFirebase();
          }
          break;
        case InternetConnectionStatus.disconnected:
          print('📵 Internet disconnected - notifications offline');
          break;
      }
    });

    // Check initial connectivity
    if (await InternetConnectionChecker().hasConnection) {
      if (!_firebaseInitialized) {
        await _initializeFirebase();
      }
    }
  }

  /// Initialize Firebase Messaging
  Future<void> _initializeFirebase() async {
    if (_firebaseInitialized) {
      print("🔥 Firebase already initialized, skipping...");
      return;
    }

    print("🔥 Initializing Firebase Messaging...");
    _firebaseInitialized = true;

    // Request Firebase permissions
    try {
      NotificationSettings settings = await _firebaseMessaging
          .requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      print('🔥 FCM permission status: ${settings.authorizationStatus}');
    } catch (e) {
      print("❌ Error requesting Firebase permissions: $e");
    }

    // Configure foreground notification presentation
    _firebaseMessaging.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    // Get FCM token
    await _getFCMToken();

    // Subscribe to language-based topic
    await ensureLanguageTopicSubscription();

    // Set up message handlers
    _setupMessageHandlers();
  }

  /// Initialize Local Notifications
  Future<void> _initializeLocal() async {
    // Configure timezone
    tz.initializeTimeZones();
    final String timeZone = await FlutterTimezone.getLocalTimezone();
    tz.setLocalLocation(tz.getLocation(timeZone));

    // Create Android notification channel
    await _localNotifications
        .resolvePlatformSpecificImplementation<
        AndroidFlutterLocalNotificationsPlugin
    >()
        ?.createNotificationChannel(_channel);

    // Initialize local notifications
    await _localNotifications.initialize(
      const InitializationSettings(
        android: AndroidInitializationSettings("@mipmap/ic_launcher"),
        iOS: DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true,
          defaultPresentSound: true,
          defaultPresentAlert: true,
          defaultPresentBadge: true,
        ),
      ),
      onDidReceiveNotificationResponse: _onLocalNotificationTapped,
    );
  }

  /// Get and manage FCM token
  Future<void> _getFCMToken() async {
    try {
      _fcmToken = await _firebaseMessaging.getToken();
      print("📱 FCM Token: $_fcmToken");

      if (Platform.isIOS) {
        var apnsToken = await _firebaseMessaging.getAPNSToken();
        print("📱 FCM APNs Token: $apnsToken");
      }

      // Save token to preferences using StorageRepository
      if (_fcmToken != null && _fcmToken!.isNotEmpty) {
        await StorageRepository.putString(StoreKeys.firebaseToken, _fcmToken!);
        print('🔑 Firebase token saved: $_fcmToken');
      } else {
        print('❌ Firebase token is empty');
      }

      // Listen for token refresh
      _firebaseMessaging.onTokenRefresh.listen((newToken) async {
        _fcmToken = newToken;
        print("🔄 FCM Token refreshed: $newToken");
        await _saveTokenToStorage(newToken);
        // Try to sync refreshed token with backend
        await syncFcmTokenWithServer();
      });
    } catch (e) {
      print("❌ Error getting FCM token: $e");
      // Try to get token from storage if network fails
      _fcmToken = StorageRepository.getString(StoreKeys.firebaseToken);
    }
  }

  /// Subscribe to language-based topic ('uz' or 'ru')
  Future<void> ensureLanguageTopicSubscription() async {
    try {
      final String lang = StorageRepository.getString(
        StoreKeys.language,
        defValue: 'uz',
      );
      final String desiredTopic = (lang.toLowerCase() == 'ru') ? 'ru' : 'uz';
      final String otherTopic = desiredTopic == 'ru' ? 'uz' : 'ru';

      await _firebaseMessaging.subscribeToTopic(desiredTopic);
      await _firebaseMessaging.unsubscribeFromTopic(otherTopic);

      print("🔔 Subscribed to topic: $desiredTopic, unsubscribed: $otherTopic");
    } catch (e) {
      print("❌ Error subscribing to language topic: $e");
    }
  }

  /// Send current FCM token to backend silently
  Future<void> syncFcmTokenWithServer() async {
    try {
      final String authToken = StorageRepository.getString(StoreKeys.token);
      if (authToken.isEmpty) {
        print('ℹ️ No auth token found, skipping FCM token sync');
        return;
      }

      if (_fcmToken == null || _fcmToken!.isEmpty) {
        await _getFCMToken();
      }
      final String tokenToSend =
          _fcmToken ?? StorageRepository.getString(StoreKeys.firebaseToken);
      if (tokenToSend.isEmpty) {
        print('ℹ️ No FCM token available, skipping sync');
        return;
      }

      final dio = serviceLocator<DioSettings>().dio;
      final response = await dio.post(
        '/clients/update-fcm-token',
        data: {'fcm_token': tokenToSend},
        options: Options(headers: {'Authorization': authToken}),
      );

      if (response.statusCode != null &&
          response.statusCode! >= 200 &&
          response.statusCode! < 300) {
        print('✅ FCM token synced with server');
      } else {
        final status = response.statusCode;
        print(
          '❌ Failed to sync FCM token. Status: $status, Body: ${response.data}',
        );
      }
    } on DioException catch (e) {
      print('❌ Network error syncing FCM token: ${e.message}');
    } catch (e) {
      print('❌ Unexpected error syncing FCM token: $e');
    }
  }

  /// Save token to storage
  Future<void> _saveTokenToStorage(String token) async {
    try {
      await StorageRepository.putString(StoreKeys.firebaseToken, token);
      print('🔑 Firebase token updated: $token');
    } catch (e) {
      print("❌ Error saving token: $e");
    }
  }

  /// Setup Firebase message handlers
  void _setupMessageHandlers() {
    // Foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Background app opened
    FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessage);

    // App launched from notification
    _handleAppLaunchedFromNotification();
  }

  /// Handle foreground messages
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    print("📨 Foreground message: ${message.messageId}");
    print("📨 Message data: ${message.data}");
    print(
      "📨 Message notification: ${message.notification?.title} - ${message.notification?.body}",
    );
    print(
      "📨 Foreground message image (Android): ${message.notification?.android?.imageUrl}",
    );
    print(
      "📨 Foreground message image (Apple): ${message.notification?.apple?.imageUrl}",
    );

    // Always show notification for foreground messages
    // Use notification data if available, otherwise use data payload
    final Map<String, dynamic> notificationData = {};

    if (message.notification != null) {
      notificationData['title'] = message.notification!.title ?? 'Echipta';
      notificationData['body'] = message.notification!.body ?? 'Yangi xabar';

      // Handle images from notification
      String? imageUrl;
      if (message.notification!.android?.imageUrl != null) {
        imageUrl = message.notification!.android!.imageUrl;
      } else if (message.notification!.apple?.imageUrl != null) {
        imageUrl = message.notification!.apple!.imageUrl;
      }

      if (imageUrl != null && imageUrl.isNotEmpty) {
        notificationData['image'] = imageUrl;
        print("📨 Found notification image: $imageUrl");
      }
    } else {
      notificationData['title'] = message.data['title'] ?? 'Echipta';
      notificationData['body'] = message.data['body'] ?? 'Yangi xabar';

      // Check for image in data payload
      if (message.data['image'] != null && message.data['image']!.isNotEmpty) {
        notificationData['image'] = message.data['image'];
        print("📨 Found data image: ${message.data['image']}");
      }
    }

    // Add all data fields for navigation
    notificationData.addAll(message.data);

    await showUniversalNotification(notificationData);
  }

  /// Handle background messages
  Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    print("📨 Background message opened: ${message.messageId}");
    // Handle navigation based on message data
    _handleNotificationNavigation(message.data);
  }

  /// Handle app launched from notification
  Future<void> _handleAppLaunchedFromNotification() async {
    RemoteMessage? initialMessage =
    await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      print("📨 App launched from notification: ${initialMessage.messageId}");
      _handleNotificationNavigation(initialMessage.data);
    }
  }

  /// Handle notification navigation based on data
  void _handleNotificationNavigation(Map<String, dynamic> data) {
    if (_navigationContext == null) {
      print("❌ Navigation context not set");
      return;
    }

    try {
      final String? type = data['type'];
      final String? id = data['id'];
      final String? route = data['route'];

      print("🧭 Navigating - Type: $type, ID: $id, Route: $route");
      print("🧭 Full notification data: $data");

      switch (type) {
        case 'event':
          // Navigate to notification screen and switch to 2nd tab (index 1) for events
          print("🎯 Event notification - navigating to notifications with tab 1");
          _navigationContext!.push('${AppRouter.notifications}?tab=1');
          break;
        case 'ticket':
          print("🎫 Ticket notification - navigating to tickets");
          _navigationContext!.push(AppRouter.tickets);
          break;
        case 'order':
          if (id != null) {
            print("📦 Order notification - navigating to order status with ID: $id");
            _navigationContext!.push(
              AppRouter.orderStatus,
              extra: int.tryParse(id),
            );
          } else {
            print("📦 Order notification without ID - going to notifications");
            _navigationContext!.push(AppRouter.notifications);
          }
          break;
        case 'match':
          if (id != null) {
            _navigationContext!.push('/match/$id');
          }
          break;
        case 'profile':
        // Navigate to main navigator and switch to profile tab
          _navigationContext!.go(AppRouter.navigator);
          break;
        case 'chat':
        // Navigate to main navigator and switch to chat tab
          _navigationContext!.go(AppRouter.navigator);
          break;
        case 'custom':
          if (route != null) {
            _navigationContext!.push(route);
          }
          break;
        default:
        // For any other notification type, navigate to notification screen without tab switching
          print("🏠 Default navigation (type: '$type') - going to notifications screen");
          _navigationContext!.push(AppRouter.notifications);
          break;
      }
    } catch (e) {
      print("❌ Navigation error: $e");
      // Fallback to home
      _navigationContext?.go(AppRouter.navigator);
    }
  }

  /// Show universal notification with image support and primary color
  /// FIXED: Reduced multiple downloads and improved iOS image support
  Future<void> showUniversalNotification(Map<String, dynamic> data) async {
    print("🔔 Attempting to show universal notification with data: $data");

    try {
      // Generate unique notification ID
      final int notificationId = DateTime.now().millisecondsSinceEpoch
          .remainder(100000);

      final String title = data['title'] ?? 'Echipta';
      final String body = data['body'] ?? 'Yangi xabar';
      final String? imageUrl = data['image'];

      print(
        "🔔 Showing notification - ID: $notificationId, Title: $title, Body: $body, Image: $imageUrl",
      );

      // Download image once if available
      ByteArrayAndroidBitmap? androidBitmap;
      String? iosImagePath;

      if (imageUrl != null && imageUrl.isNotEmpty) {
        print("🖼️ Processing image for notification: $imageUrl");

        // Download image data once
        final imageData = await _downloadImageData(imageUrl);
        if (imageData != null) {
          // Create Android bitmap
          androidBitmap = ByteArrayAndroidBitmap(imageData);

          // Save for iOS if needed
          if (Platform.isIOS) {
            iosImagePath = await _saveImageDataForIOS(
                imageData,
                'noti_img_${DateTime.now().millisecondsSinceEpoch}.jpg'
            );
          }
        }
      }

      // Android notification details with optimized image handling
      AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
        'echipta_notifications',
        'Echipta Notifications',
        channelDescription: 'Echipta ilovasi uchun bildirishnomalar',
        importance: Importance.max,
        priority: Priority.high,
        showWhen: true,
        enableVibration: true,
        playSound: true,
        icon: '@drawable/ic_notification',
        color: const Color(0xFF342783),
        colorized: true,
        largeIcon: androidBitmap,
        styleInformation: androidBitmap != null
            ? BigPictureStyleInformation(
          androidBitmap,
          largeIcon: androidBitmap,
          contentTitle: title,
          htmlFormatContentTitle: true,
          summaryText: body,
          htmlFormatSummaryText: true,
        )
            : BigTextStyleInformation(
          body,
          htmlFormatBigText: true,
          contentTitle: title,
          htmlFormatContentTitle: true,
        ),
      );

      // iOS notification details with improved attachment handling
      List<DarwinNotificationAttachment>? iosAttachments;
      if (Platform.isIOS && iosImagePath != null) {
        try {
          iosAttachments = [
            DarwinNotificationAttachment(
              iosImagePath,
              identifier: 'image_${DateTime.now().millisecondsSinceEpoch}',
            )
          ];
          print("🍎 iOS attachment created: $iosImagePath");
        } catch (e) {
          print("❌ Error creating iOS attachment: $e");
          iosAttachments = null;
        }
      }

      DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        attachments: iosAttachments,
        categoryIdentifier: 'echipta_category',
      );

      final NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // Create a structured payload for better parsing
      final Map<String, String> payloadData = {};
      data.forEach((key, value) {
        payloadData[key] = value?.toString() ?? '';
      });

      final String payloadString = payloadData.entries.map((e) => '${e.key}:${e.value}').join('|');
      print("📦 Creating notification payload: $payloadString");
      print("📦 Original data: $data");
      print("📦 Processed payload data: $payloadData");

      await _localNotifications.show(
        notificationId,
        title,
        body,
        notificationDetails,
        payload: payloadString,
      );

      print("✅ Universal notification shown successfully: $notificationId");
    } catch (e) {
      print("❌ Error showing universal notification: $e");
      // Fallback to simple notification if image loading fails
      await showLocalNotification(data);
    }
  }

  /// Download image data once to avoid multiple downloads
  Future<Uint8List?> _downloadImageData(String url) async {
    try {
      print("🖼️ Downloading image data: $url");

      final response = await http
          .get(Uri.parse(url))
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final Uint8List bytes = response.bodyBytes;
        print("✅ Image downloaded successfully, size: ${bytes.length} bytes");
        return bytes;
      } else {
        print(
          "❌ Failed to download image, status code: ${response.statusCode}",
        );
        return null;
      }
    } catch (e) {
      print("❌ Error downloading image: $e");
      return null;
    }
  }

  /// Save image data to file for iOS attachments
  Future<String?> _saveImageDataForIOS(Uint8List imageData, String fileName) async {
    try {
      print("🍎 Saving image data for iOS attachment: $fileName");

      final Directory dir = await getTemporaryDirectory();
      final String filePath = '${dir.path}/$fileName';
      final file = File(filePath);

      await file.writeAsBytes(imageData);

      // Verify file exists and has content
      if (await file.exists() && await file.length() > 0) {
        print("✅ iOS image saved successfully: $filePath (${await file.length()} bytes)");
        return filePath;
      } else {
        print("❌ iOS image file verification failed");
        return null;
      }
    } catch (e) {
      print("❌ Error saving iOS image: $e");
      return null;
    }
  }

  /// Show local notification using Flutter Local Notifications (fallback method)
  Future<void> showLocalNotification(Map<String, dynamic> data) async {
    print("🔔 Attempting to show local notification with data: $data");

    try {
      // Generate unique notification ID
      final int notificationId = DateTime.now().millisecondsSinceEpoch
          .remainder(100000);

      const AndroidNotificationDetails androidDetails =
      AndroidNotificationDetails(
        'echipta_notifications',
        'Echipta Notifications',
        channelDescription: 'Echipta ilovasi uchun bildirishnomalar',
        importance: Importance.max,
        priority: Priority.high,
        showWhen: true,
        enableVibration: true,
        playSound: true,
        icon: '@drawable/ic_notification',
        color: Color(0xFF342783),
        colorized: true,
      );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      final String title = data['title'] ?? 'Echipta';
      final String body = data['body'] ?? 'Yangi xabar';

      print(
        "🔔 Showing notification - ID: $notificationId, Title: $title, Body: $body",
      );

      // Create a structured payload for better parsing
      final Map<String, String> payloadData = {};
      data.forEach((key, value) {
        payloadData[key] = value?.toString() ?? '';
      });

      final String payloadString = payloadData.entries.map((e) => '${e.key}:${e.value}').join('|');
      print("📦 Creating fallback notification payload: $payloadString");
      print("📦 Original fallback data: $data");
      print("📦 Processed fallback payload data: $payloadData");

      await _localNotifications.show(
        notificationId,
        title,
        body,
        notificationDetails,
        payload: payloadString,
      );

      print("✅ Local notification shown successfully: $notificationId");
    } catch (e) {
      print("❌ Error showing local notification: $e");
    }
  }

  /// Show simple notification
  Future<void> showSimpleNotification({
    required String title,
    required String body,
    Map<String, String>? payload,
  }) async {
    await showUniversalNotification({
      'title': title,
      'body': body,
      ...?payload,
    });
  }

  /// Test notification to verify everything is working
  Future<void> showTestNotification() async {
    print("🧪 Showing test notification...");
    await showUniversalNotification({
      'title': 'Test Notification',
      'body': 'Bu test bildirishnomasi - agar ko\'rsangiz, hammasi ishlayapti!',
      'type': 'test',
    });
  }

  /// Test notification with image - OPTIMIZED: No more multiple downloads!
  Future<void> showTestNotificationWithImage() async {
    print("🧪 Showing test notification with image...");
    await showUniversalNotification({
      'title': 'Test with Image',
      'body': 'Bu rasm bilan test bildirishnomasi',
      'image': 'https://picsum.photos/400/200', // Sample image URL
      'type': 'test_image',
    });
  }

  /// Test event-type notification to verify tab navigation
  Future<void> showTestEventNotification() async {
    print("🧪 Showing test event notification...");
    await showUniversalNotification({
      'title': 'Xarid!',
      'body': 'Siz muvaffaqiyatli chipta sotib oldingiz! O\'yin kuni kutib qolamiz!',
      'type': 'event',
    });
  }

  /// Test regular notification to verify default navigation
  Future<void> showTestRegularNotification() async {
    print("🧪 Showing test regular notification...");
    await showUniversalNotification({
      'title': 'Yangi xabar',
      'body': 'Bu oddiy bildirishnoma - bildirishnomalar sahifasiga olib boradi',
      'type': 'news',
    });
  }

  /// Test notification without type to verify default behavior
  Future<void> showTestNotificationWithoutType() async {
    print("🧪 Showing test notification without type...");
    await showUniversalNotification({
      'title': 'Tizim xabari',
      'body': 'Bu type bo\'lmagan bildirishnoma',
    });
  }

  /// Test ticket notification to verify ticket navigation
  Future<void> showTestTicketNotification() async {
    print("🧪 Showing test ticket notification...");
    await showUniversalNotification({
      'title': 'Chipta xabari',
      'body': 'Sizning chiptangiz haqida yangilik!',
      'type': 'ticket',
    });
  }

  /// Test order notification to verify order navigation
  Future<void> showTestOrderNotification() async {
    print("🧪 Showing test order notification...");
    await showUniversalNotification({
      'title': 'Buyurtma xabari',
      'body': 'Sizning buyurtmangiz haqida yangilik!',
      'type': 'order',
      'id': '12345',
    });
  }

  /// Local notification tap handler
  void _onLocalNotificationTapped(NotificationResponse response) {
    print("🔔 Local notification tapped: ${response.payload}");

    if (response.payload != null && _navigationContext != null) {
      try {
        // Parse the structured payload string
        final String payloadString = response.payload!;
        print("🔍 Parsing payload: $payloadString");

        Map<String, dynamic> notificationData = {};

        // Parse the key:value pairs separated by |
        if (payloadString.isNotEmpty) {
          final pairs = payloadString.split('|');
          for (final pair in pairs) {
            final parts = pair.split(':');
            if (parts.length >= 2) {
              final key = parts[0].trim();
              final value = parts.sublist(1).join(':').trim(); // Handle values with colons
              notificationData[key] = value;
            }
          }
        }

        print("🎯 Parsed notification data: $notificationData");

        // Handle navigation based on extracted data
        _handleNotificationNavigation(notificationData);
      } catch (e) {
        print("❌ Error handling notification tap: $e");
        // Fallback to notifications screen
        _navigationContext!.push(AppRouter.notifications);
      }
    } else {
      // If no payload or context, fallback to notifications screen
      if (_navigationContext != null) {
        _navigationContext!.push(AppRouter.notifications);
      }
    }
  }

  /// Reset badge counter (iOS only)
  Future<void> resetBadgeCounter() async {
    // This is handled by the system for Flutter Local Notifications
    print("📱 Badge counter reset requested");
  }

  /// Cancel all notifications
  Future<void> cancelAllNotifications() async {
    await _localNotifications.cancelAll();
    print("🗑️ All notifications canceled");
  }

  /// Cancel notification by ID
  Future<void> cancelNotification(int id) async {
    await _localNotifications.cancel(id);
    print("🗑️ Notification $id canceled");
  }

  /// Check if notifications are allowed
  Future<bool> areNotificationsAllowed() async {
    try {
      // Check Android notification permission (Android 13+)
      final Permission notificationPermission = Permission.notification;
      final PermissionStatus status = await notificationPermission.status;

      print("📱 Notification permission status: $status");
      return status == PermissionStatus.granted;
    } catch (e) {
      print("❌ Error checking notification permission: $e");
      return false;
    }
  }

  /// Request notification permissions (for manual permission request)
  Future<bool> requestPermissions() async {
    try {
      print("📱 Manually requesting notification permissions...");

      // Check current status first
      final Permission notificationPermission = Permission.notification;
      PermissionStatus status = await notificationPermission.status;

      if (status == PermissionStatus.granted) {
        print("📱 Permissions already granted");
        return true;
      }

      // Request permission if not granted
      status = await notificationPermission.request();
      print("📱 Manual permission request result: $status");

      if (status == PermissionStatus.granted) {
        // Initialize Firebase if not already done
        if (!_firebaseInitialized) {
          await _initializeFirebase();
        }
        return true;
      }

      return status == PermissionStatus.granted;
    } catch (e) {
      print("❌ Error requesting notification permissions: $e");
      return false;
    }
  }

  /// Dispose notification service
  static Future<void> dispose() async {
    await NotificationService()._connectivitySubscription?.cancel();
    _navigationContext = null;
    _initialized = false;
    _firebaseInitialized = false;
    print("🗑️ Notification Service disposed");
  }
}

/// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  print("📨 Background message received: ${message.messageId}");
  print("📨 Background message data: ${message.data}");
  print(
    "📨 Background message notification: ${message.notification?.title} - ${message.notification?.body}",
  );
  print(
    "📨 Background message image (Android): ${message.notification?.android?.imageUrl}",
  );
  print(
    "📨 Background message image (Apple): ${message.notification?.apple?.imageUrl}",
  );

  // Always show notification for background messages
  final Map<String, dynamic> notificationData = {};

  if (message.notification != null) {
    notificationData['title'] = message.notification!.title ?? 'Echipta';
    notificationData['body'] = message.notification!.body ?? 'Yangi xabar';

    // Handle images from notification
    String? imageUrl;
    if (message.notification!.android?.imageUrl != null) {
      imageUrl = message.notification!.android!.imageUrl;
    } else if (message.notification!.apple?.imageUrl != null) {
      imageUrl = message.notification!.apple!.imageUrl;
    }

    if (imageUrl != null && imageUrl.isNotEmpty) {
      notificationData['image'] = imageUrl;
      print("📨 Found background notification image: $imageUrl");
    }
  } else {
    notificationData['title'] = message.data['title'] ?? 'Echipta';
    notificationData['body'] = message.data['body'] ?? 'Yangi xabar';

    // Check for image in data payload
    if (message.data['image'] != null && message.data['image']!.isNotEmpty) {
      notificationData['image'] = message.data['image'];
      print("📨 Found background data image: ${message.data['image']}");
    }
  }

  // Add all data fields for navigation
  notificationData.addAll(message.data);

  await NotificationService().showUniversalNotification(notificationData);
}